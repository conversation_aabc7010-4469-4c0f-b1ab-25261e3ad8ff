'use client';

import React, { useRef, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  HomeIcon,
  UsersIcon,
  BuildingStorefrontIcon,
  TruckIcon,
  ShoppingBagIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CogIcon,
  BellIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  XMarkIcon,

  PresentationChartLineIcon,
  BanknotesIcon,
  ShieldCheckIcon,
  MegaphoneIcon,
  PhotoIcon,
  PencilSquareIcon,
  TagIcon,
  GiftIcon,
  StarIcon,
  ChatBubbleLeftRightIcon,
  ClockIcon,
  MapPinIcon,
  CreditCardIcon,
  ChartPieIcon,
  DocumentChartBarIcon,
  AdjustmentsHorizontalIcon,
  KeyIcon,
  UserCircleIcon,
} from '@heroicons/react/24/outline';

// Type definitions for navigation structure
interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface NavigationCategory {
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  items: NavigationItem[];
}

// Professional menu categorization structure with category icons
const navigationCategories: NavigationCategory[] = [
  {
    name: 'Overview',
    icon: HomeIcon,
    items: [
      { name: 'Dashboard', href: '/admin/dashboard', icon: HomeIcon },
    ]
  },
  {
    name: 'User Management',
    icon: UsersIcon,
    items: [
      { name: 'All Users', href: '/admin/users', icon: UsersIcon },
      { name: 'Customers', href: '/admin/customers', icon: UserGroupIcon },
      { name: 'Vendors', href: '/admin/vendors', icon: BuildingStorefrontIcon },
      { name: 'Drivers', href: '/admin/drivers', icon: TruckIcon },
    ]
  },
  {
    name: 'Operations',
    icon: ShoppingBagIcon,
    items: [
      { name: 'Orders', href: '/admin/orders', icon: ShoppingBagIcon },
      { name: 'Disputes', href: '/admin/disputes', icon: ExclamationTriangleIcon },
      { name: 'Notifications', href: '/admin/notifications', icon: BellIcon },
      { name: 'Reviews', href: '/admin/reviews', icon: StarIcon },
      { name: 'Support Chat', href: '/admin/support', icon: ChatBubbleLeftRightIcon },
    ]
  },
  {
    name: 'Content Management',
    icon: PencilSquareIcon,
    items: [
      { name: 'CMS Dashboard', href: '/admin/cms-dashboard', icon: PencilSquareIcon },
      { name: 'Media Library', href: '/admin/cms/media', icon: PhotoIcon },
      { name: 'Promotions', href: '/admin/cms/promotions', icon: MegaphoneIcon },
      { name: 'Banners', href: '/admin/cms/banners', icon: TagIcon },
    ]
  },
  {
    name: 'Marketing',
    icon: MegaphoneIcon,
    items: [
      { name: 'Campaigns', href: '/admin/marketing/campaigns', icon: MegaphoneIcon },
      { name: 'Coupons', href: '/admin/marketing/coupons', icon: GiftIcon },
      { name: 'Loyalty Program', href: '/admin/marketing/loyalty', icon: StarIcon },
      { name: 'Push Notifications', href: '/admin/marketing/push', icon: BellIcon },
    ]
  },
  {
    name: 'Analytics & Reports',
    icon: ChartBarIcon,
    items: [
      { name: 'Overview Analytics', href: '/admin/analytics', icon: ChartBarIcon },
      { name: 'Sales Reports', href: '/admin/reports/sales', icon: DocumentChartBarIcon },
      { name: 'User Reports', href: '/admin/reports/users', icon: ChartPieIcon },
      { name: 'Performance', href: '/admin/reports/performance', icon: PresentationChartLineIcon },
      { name: 'Custom Reports', href: '/admin/reports/custom', icon: DocumentTextIcon },
    ]
  },
  {
    name: 'Financial',
    icon: CurrencyDollarIcon,
    items: [
      { name: 'Payouts', href: '/admin/payouts', icon: CurrencyDollarIcon },
      { name: 'Transactions', href: '/admin/financial/transactions', icon: CreditCardIcon },
      { name: 'Revenue', href: '/admin/financial/revenue', icon: BanknotesIcon },
      { name: 'Commissions', href: '/admin/financial/commissions', icon: ChartPieIcon },
    ]
  },
  {
    name: 'Logistics',
    icon: TruckIcon,
    items: [
      { name: 'Delivery Zones', href: '/admin/logistics/zones', icon: MapPinIcon },
      { name: 'Delivery Times', href: '/admin/logistics/times', icon: ClockIcon },
      { name: 'Fleet Management', href: '/admin/logistics/fleet', icon: TruckIcon },
    ]
  },
  {
    name: 'System',
    icon: CogIcon,
    items: [
      { name: 'General Settings', href: '/admin/settings', icon: CogIcon },
      { name: 'API Configuration', href: '/admin/settings/api', icon: AdjustmentsHorizontalIcon },
      { name: 'Security', href: '/admin/settings/security', icon: ShieldCheckIcon },
      { name: 'Admin Accounts', href: '/admin/settings/admins', icon: UserCircleIcon },
      { name: 'Access Keys', href: '/admin/settings/keys', icon: KeyIcon },
    ]
  }
];

interface AdminSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
  onExpandAndNavigate?: (href: string, categoryName: string) => void;
}

export default function AdminSidebar({
  isOpen,
  onClose,
  isCollapsed = false,
  onToggleCollapse,
  onExpandAndNavigate
}: AdminSidebarProps) {
  const pathname = usePathname();

  // YouTube-style independent scroll containers
  const expandedContainerRef = useRef<HTMLDivElement>(null);
  const collapsedContainerRef = useRef<HTMLDivElement>(null);

  // Track if initial scroll restoration has been completed
  const initialScrollRestoredRef = useRef<{ expanded: boolean; collapsed: boolean }>({
    expanded: false,
    collapsed: false
  });

  const isItemActive = (href: string) => {
    return pathname === href || (href === '/admin/dashboard' && pathname === '/admin');
  };

  // Initial scroll restoration on component mount
  useEffect(() => {
    // Restore scroll position for the initially visible container
    const restoreInitialScroll = () => {
      if (isCollapsed && !initialScrollRestoredRef.current.collapsed) {
        const container = collapsedContainerRef.current;
        if (container) {
          const savedScroll = sessionStorage.getItem('admin-sidebar-collapsed-scroll');
          if (savedScroll) {
            const scrollPosition = parseInt(savedScroll, 10);
            if (!isNaN(scrollPosition) && scrollPosition >= 0) {
              container.scrollTop = scrollPosition;
              console.log('🔄 Initial collapsed scroll position:', scrollPosition);
            }
          }
          initialScrollRestoredRef.current.collapsed = true;
        }
      } else if (!isCollapsed && !initialScrollRestoredRef.current.expanded) {
        const container = expandedContainerRef.current;
        if (container) {
          const savedScroll = sessionStorage.getItem('admin-sidebar-expanded-scroll');
          if (savedScroll) {
            const scrollPosition = parseInt(savedScroll, 10);
            if (!isNaN(scrollPosition) && scrollPosition >= 0) {
              container.scrollTop = scrollPosition;
              console.log('🔄 Initial expanded scroll position:', scrollPosition);
            }
          }
          initialScrollRestoredRef.current.expanded = true;
        }
      }
    };

    // Use requestAnimationFrame to ensure DOM is ready
    requestAnimationFrame(restoreInitialScroll);
  }, []); // Run only on mount

  // Enhanced scroll restoration - CRITICAL FIX: Save current position and restore target position
  useEffect(() => {
    console.log('🔄 Sidebar state changing to:', isCollapsed ? 'collapsed' : 'expanded');

    if (isCollapsed) {
      // FIRST: Save the current expanded scroll position immediately
      const expandedContainer = expandedContainerRef.current;
      if (expandedContainer) {
        const currentScroll = expandedContainer.scrollTop;
        sessionStorage.setItem('admin-sidebar-expanded-scroll', currentScroll.toString());
        console.log('💾 IMMEDIATE: Saved expanded scroll position:', currentScroll);
      }

      // THEN: Set the scroll position on the collapsed container (use requestAnimationFrame for timing)
      requestAnimationFrame(() => {
        const collapsedContainer = collapsedContainerRef.current;
        if (collapsedContainer) {
          const savedScroll = sessionStorage.getItem('admin-sidebar-collapsed-scroll');
          if (savedScroll) {
            const scrollPosition = parseInt(savedScroll, 10);
            if (!isNaN(scrollPosition) && scrollPosition >= 0) {
              collapsedContainer.scrollTop = scrollPosition;
              console.log('🔄 RESTORED: Set collapsed scroll position:', scrollPosition);
            }
          }
        }
      });
    } else {
      // FIRST: Save the current collapsed scroll position immediately
      const collapsedContainer = collapsedContainerRef.current;
      if (collapsedContainer) {
        const currentScroll = collapsedContainer.scrollTop;
        sessionStorage.setItem('admin-sidebar-collapsed-scroll', currentScroll.toString());
        console.log('💾 IMMEDIATE: Saved collapsed scroll position:', currentScroll);
      }

      // THEN: Set the scroll position on the expanded container (use requestAnimationFrame for timing)
      requestAnimationFrame(() => {
        const expandedContainer = expandedContainerRef.current;
        if (expandedContainer) {
          const savedScroll = sessionStorage.getItem('admin-sidebar-expanded-scroll');
          if (savedScroll) {
            const scrollPosition = parseInt(savedScroll, 10);
            if (!isNaN(scrollPosition) && scrollPosition >= 0) {
              expandedContainer.scrollTop = scrollPosition;
              console.log('🔄 RESTORED: Set expanded scroll position:', scrollPosition);
            }
          }
        }
      });
    }
  }, [isCollapsed]);

  // Throttled scroll tracking for better performance
  const throttleRef = useRef<{ expanded: number | null; collapsed: number | null }>({
    expanded: null,
    collapsed: null
  });

  // Track scroll position for expanded view with throttling
  const handleExpandedScroll = () => {
    const container = expandedContainerRef.current;
    if (container) {
      // Clear existing timeout
      if (throttleRef.current.expanded) {
        clearTimeout(throttleRef.current.expanded);
      }

      // Throttle scroll position saving to avoid excessive sessionStorage writes
      throttleRef.current.expanded = window.setTimeout(() => {
        try {
          sessionStorage.setItem('admin-sidebar-expanded-scroll', container.scrollTop.toString());
          // Debug logging (remove in production)
          console.log('💾 Saved expanded scroll position:', container.scrollTop);
        } catch (error) {
          console.warn('Failed to save expanded sidebar scroll position:', error);
        }
      }, 100); // 100ms throttle
    }
  };

  // Track scroll position for collapsed view with throttling
  const handleCollapsedScroll = () => {
    const container = collapsedContainerRef.current;
    if (container) {
      // Clear existing timeout
      if (throttleRef.current.collapsed) {
        clearTimeout(throttleRef.current.collapsed);
      }

      // Throttle scroll position saving to avoid excessive sessionStorage writes
      throttleRef.current.collapsed = window.setTimeout(() => {
        try {
          sessionStorage.setItem('admin-sidebar-collapsed-scroll', container.scrollTop.toString());
          // Debug logging (remove in production)
          console.log('💾 Saved collapsed scroll position:', container.scrollTop);
        } catch (error) {
          console.warn('Failed to save collapsed sidebar scroll position:', error);
        }
      }, 100); // 100ms throttle
    }
  };

  // Save scroll positions immediately when page visibility changes or before unload
  useEffect(() => {
    const saveCurrentScrollPositions = () => {
      try {
        // Save expanded scroll position if container exists
        const expandedContainer = expandedContainerRef.current;
        if (expandedContainer && showExpanded) {
          sessionStorage.setItem('admin-sidebar-expanded-scroll', expandedContainer.scrollTop.toString());
        }

        // Save collapsed scroll position if container exists
        const collapsedContainer = collapsedContainerRef.current;
        if (collapsedContainer && showCollapsed) {
          sessionStorage.setItem('admin-sidebar-collapsed-scroll', collapsedContainer.scrollTop.toString());
        }
      } catch (error) {
        console.warn('Failed to save scroll positions on visibility change:', error);
      }
    };

    const handleVisibilityChange = () => {
      if (document.hidden) {
        saveCurrentScrollPositions();
      }
    };

    const handleBeforeUnload = () => {
      saveCurrentScrollPositions();
    };

    // Add event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('beforeunload', handleBeforeUnload);

    // Cleanup function
    return () => {
      // Save positions one final time before cleanup
      saveCurrentScrollPositions();

      // Clear throttled timeouts - capture current values to avoid stale closure
      const currentThrottleRef = throttleRef.current;
      if (currentThrottleRef.expanded) {
        clearTimeout(currentThrottleRef.expanded);
      }
      if (currentThrottleRef.collapsed) {
        clearTimeout(currentThrottleRef.collapsed);
      }

      // Remove event listeners
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const handleCollapsedCategoryClick = (category: NavigationCategory) => {
    if (onExpandAndNavigate && category.items.length > 0) {
      // Get the first item in the category
      const firstItem = category.items[0];
      // Expand sidebar and navigate to first item
      onExpandAndNavigate(firstItem.href, category.name);
    }
  };

  // Handle navigation link clicks - only close sidebar on mobile
  const handleNavClick = () => {
    // Only close sidebar on mobile (when screen is small)
    if (window.innerWidth < 1024) { // lg breakpoint
      onClose();
    }
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar - Positioned BELOW header */}
      <div className={`fixed left-0 z-30 bg-white shadow-lg transform transition-all duration-300 ease-in-out lg:translate-x-0 ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      } ${isCollapsed ? 'w-16' : 'w-64'}`} style={{ top: '64px', height: 'calc(100vh - 64px)' }}>
        {/* Mobile Close Button Header */}
        <div className="lg:hidden flex items-center justify-end px-4 py-3 border-b border-gray-200 bg-white">
          <button
            onClick={onClose}
            className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* FIXED: Both containers always in DOM, use CSS visibility */}

        {/* Collapsed View - Always in DOM, visibility controlled by CSS */}
        <div
          ref={collapsedContainerRef}
          onScroll={handleCollapsedScroll}
          className="flex-1 overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400"
          style={{
            height: 'calc(100vh - 64px)',
            display: isCollapsed ? 'block' : 'none'
          }}
        >
          <nav className="py-3 px-2">
            <div className="space-y-1">
              {navigationCategories.map((category) => (
                <div key={category.name} className="relative group">
                  <button
                    onClick={() => handleCollapsedCategoryClick(category)}
                    className="w-full flex items-center justify-center p-3 rounded-md transition-all duration-200 hover:scale-105 text-gray-600 hover:bg-orange-50 hover:text-orange-600 hover:shadow-sm"
                    title={`${category.name} - Click to expand and view ${category.items[0]?.name || 'items'}`}
                  >
                    <category.icon className="h-6 w-6" />
                  </button>

                  {/* Enhanced Tooltip */}
                  <div className="absolute left-full ml-3 top-0 bg-gray-900 text-white text-sm px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50 shadow-lg">
                    <div className="font-medium">{category.name}</div>
                    <div className="text-xs text-gray-300 mt-1">
                      Click to expand & go to {category.items[0]?.name || 'first item'}
                    </div>
                    <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-gray-900 rotate-45"></div>
                  </div>
                </div>
              ))}
            </div>
            {/* Bottom Spacer */}
            <div className="h-6"></div>
          </nav>
        </div>

        {/* Expanded View - Always in DOM, visibility controlled by CSS */}
        <div
          ref={expandedContainerRef}
          onScroll={handleExpandedScroll}
          className="flex-1 overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400"
          style={{
            height: 'calc(100vh - 64px)',
            display: isCollapsed ? 'none' : 'block'
          }}
        >
          <nav className="py-3 px-4">
            <div className="space-y-1">
              {navigationCategories.map((category) => (
                <div key={category.name}>
                  {/* Category Header */}
                  <div className="category-header px-3 py-2 text-gray-700">
                    <span className="category-text uppercase tracking-wide text-sm font-bold leading-tight">
                      {category.name}
                    </span>
                  </div>

                  {/* Category Items */}
                  <div className="ml-3 space-y-1 mt-1 mb-4">
                    {category.items.map((item) => {
                      const isActive = isItemActive(item.href);
                      return (
                        <Link
                          key={item.name}
                          href={item.href}
                          onClick={handleNavClick}
                          className={`group flex items-center px-3 py-2.5 text-sm font-medium rounded-md transition-colors ${
                            isActive
                              ? 'bg-orange-100 text-orange-700 border-r-2 border-orange-500'
                              : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                          }`}
                        >
                          <item.icon
                            className={`mr-3 h-4 w-4 ${
                              isActive ? 'text-orange-500' : 'text-gray-400 group-hover:text-gray-500'
                            }`}
                          />
                          <span className="text-sm font-normal">{item.name}</span>
                        </Link>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
            {/* Bottom Spacer */}
            <div className="h-6"></div>
          </nav>
        </div>
      </div>
    </>
  );
}
